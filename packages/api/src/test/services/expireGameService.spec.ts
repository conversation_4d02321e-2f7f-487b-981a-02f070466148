import { SinonStub, stub } from "sinon";
import config from "../../skywind/config";
import { FACTORY } from "../factories/common";
import { getDynamicDomainService } from "../../skywind/services/domain";
import { setDynamicDomain, truncate } from "../entities/helper";
import { BrandEntity } from "../../skywind/entities/brand";
import { expect, should, use } from "chai";
import { verifyInternalToken } from "../../skywind/utils/token";
import * as sinon<PERSON>hai from "sinon-chai";
import ExpireGameService from "../../skywind/services/expireGameService";
import * as gameService from "../../skywind/services/game";
import EntitySettingsService from "../../skywind/services/settings";
import { BrandFinalizationType, GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { OperationForbidden } from "../../skywind/errors";
import { Models } from "../../skywind/models/models";
import * as DynamicDomainCache from "../../skywind/cache/dynamicDomainCache";


const chaiAsPromise = require("chai-as-promised");
const FactoryGirl = require("factory-girl");
const request = require("request");
const gameProvider = require("../../skywind/services/gameprovider");

use(sinonChai);
should();
use(chaiAsPromise);

describe("Expire game", () => {

    let requestPostMock: SinonStub;
    let brand: BrandEntity;
    let brandWithManualPayments: BrandEntity;
    const factory = FactoryGirl.factory;
    let getGameMock: SinonStub;
    let findOneEntityGameMock: SinonStub;
    let getDeploymentGroupModelMock: SinonStub;

    before(async () => {
        await truncate();

        config.gameHistory.redshift = config.db;
        config.liveDeploymentGroupEnabled = false;

        brand = await factory.create(FACTORY.BRAND);
        brandWithManualPayments = await factory.create(FACTORY.BRAND);
        new EntitySettingsService(brandWithManualPayments).update(
            { finalizationSupport: BrandFinalizationType.MANUAL_PAYMENTS } as any
        );

        const domain = await getDynamicDomainService().create({
            domain: "gc.gameserver.skywindgroup.com",
            environment: "gc"
        });

        await setDynamicDomain(brand, domain);
        await setDynamicDomain(brandWithManualPayments, domain);
        getGameMock = stub(gameProvider, "getGame");
        findOneEntityGameMock = stub(gameService, "findOneEntityGame");
        getDeploymentGroupModelMock = stub(Models.DeploymentGroupModel, "findOne");

        requestPostMock = stub(request, "post");
    });

    beforeEach(() => {
        requestPostMock.reset();
    });

    after(() => {
        requestPostMock.restore();
        getGameMock.restore();
        findOneEntityGameMock.restore();
        getDeploymentGroupModelMock.restore();
    });

    it("Expire game", async () => {
        DynamicDomainCache.reset();
        requestPostMock.yields(null, { statusCode: 200 }, []);
        getGameMock.returns({ features: { gameFinalizationType: GameFinalizationType.FINALIZATION } });
        findOneEntityGameMock.returns({ game: {}, isSuspended: () => false});
        await (new EntitySettingsService(brand)).patch({
            finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS
        });

        await ExpireGameService.expire({
            brandId: brand.id,
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            roundId: "1000",
            playerCode: "player001"
        });

        expect(requestPostMock.args[0][0])
            .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
        const expectedTokenInfo = {
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            finalizeBrokenPayment: "retry",
            operatorSupportsFinalization: true
        };

        const tokenData = await verifyInternalToken(requestPostMock.args[0][1].body.token);
        expect(tokenData).deep.include(expectedTokenInfo);
    });

    it("Expire game with deployment group - route param shall be present in URL to gameserver", async () => {
        DynamicDomainCache.reset();
        requestPostMock.yields(null, { statusCode: 200 }, []);
        getGameMock.returns({ features: { gameFinalizationType: GameFinalizationType.FINALIZATION } });
        findOneEntityGameMock.returns({ game: { deploymentGroupId: 1111 }, isSuspended: () => false});
        getDeploymentGroupModelMock.resolves({
            id: 1,
            route: "my_route",
            toInfo: () => {
                return {
                    id: 1,
                    route: "my_route",
                    description: null,
                    type: null
                }
            }
        });
        await (new EntitySettingsService(brand)).patch({
            finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS
        });

        await ExpireGameService.expire({
            brandId: brand.id,
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            roundId: "1000",
            playerCode: "player001"
        });

        expect(requestPostMock.args[0][0])
            .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
        expect(requestPostMock.args[0][1].qs.sw_deployment).to.exist;
        expect(requestPostMock.args[0][1].qs.sw_deployment).to.eq("my_route");
        const expectedTokenInfo = {
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            finalizeBrokenPayment: "retry",
            operatorSupportsFinalization: true
        };

        const tokenData = await verifyInternalToken(requestPostMock.args[0][1].body.token);
        expect(tokenData).deep.include(expectedTokenInfo);
    });

    it("Expire game - manual payments, game finalization type empty", async () => {
        getGameMock.returns({});
        findOneEntityGameMock.returns({ game: {}, isSuspended: () => false});

        await ExpireGameService.expire({
            brandId: brandWithManualPayments.id,
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            roundId: "1000",
            playerCode: "player001"
        }).should.eventually.rejectedWith(OperationForbidden);
    });

    it("Expire game - manual payments, game finalization type NONE", async () => {
        getGameMock.returns({});
        findOneEntityGameMock.returns({
            game: { features: { gameFinalizationType: GameFinalizationType.NONE } },
            isSuspended: () => false
        });

        await ExpireGameService.expire({
            brandId: brandWithManualPayments.id,
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            roundId: "1000",
            playerCode: "player001"
        }).should.eventually.rejectedWith(OperationForbidden);
    });

    it("Expire game - manual payments", async () => {
        requestPostMock.yields(null, { statusCode: 200 }, []);
        getGameMock.returns({ features: { gameFinalizationType: GameFinalizationType.FINALIZATION } });
        findOneEntityGameMock.returns({ game: {}, isSuspended: () => false});

        await ExpireGameService.expire({
            brandId: brandWithManualPayments.id,
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            roundId: "1000",
            playerCode: "player001"
        });

        const expectedTokenInfo = {
            gameContextId: "some_game_context_id",
            gameCode: "testGame",
            finalizeBrokenPayment: "markFinalized",
            operatorSupportsFinalization: true,
            lockContext: true,
            brandFinalizationType: "manualPayments"
        };

        const tokenData = await verifyInternalToken(requestPostMock.args[0][1].body.token);
        expect(tokenData).deep.include(expectedTokenInfo);
    });
});
